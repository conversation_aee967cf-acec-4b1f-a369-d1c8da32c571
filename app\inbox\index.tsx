import React, { useEffect } from "react";
import {
  StyleSheet,
  View,
  Text,
  FlatList,
  ScrollView,
  RefreshControl,
} from "react-native";
import { Colors } from "@/constants/Colors";
import { AnnouncementItem } from "@/components/AnnouncementItem";
import { useRouter } from "expo-router";
import { AnnouncementsQuery, useAnnouncementsQuery } from "@/generated/graphql";
import { Ionicons } from "@expo/vector-icons";
import LoadingScreen from "@/components/LoadingView";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  Easing,
} from "react-native-reanimated";

export default function InboxScreen() {
  const router = useRouter();
  const {
    data: announcementsData,
    isLoading,
    refetch,
  } = useAnnouncementsQuery();

  const handleAnnouncementPress = (
    announcement: AnnouncementsQuery["anouncements"][number]
  ) => {
    router.push(`/inbox/${announcement.id}` as any);
  };

  // Show loading screen while fetching data
  if (isLoading) {
    return <LoadingScreen />;
  }

  // Check if there are no announcements
  const hasAnnouncements =
    announcementsData?.anouncements &&
    announcementsData.anouncements.length > 0;

  // Empty state component with animations
  const EmptyState = () => {
    const opacity = useSharedValue(0);
    const translateY = useSharedValue(30);
    const iconScale = useSharedValue(0.8);

    useEffect(() => {
      // Animate entrance
      opacity.value = withTiming(1, {
        duration: 600,
        easing: Easing.out(Easing.ease),
      });
      translateY.value = withSpring(0, { damping: 15, stiffness: 100 });
      iconScale.value = withSpring(1, { damping: 12, stiffness: 120 });
    }, []);

    const animatedStyle = useAnimatedStyle(() => ({
      opacity: opacity.value,
      transform: [{ translateY: translateY.value }],
    }));

    const iconAnimatedStyle = useAnimatedStyle(() => ({
      transform: [{ scale: iconScale.value }],
    }));

    return (
      <Animated.View style={[styles.emptyContainer, animatedStyle]}>
        <Animated.View style={[styles.emptyIconContainer, iconAnimatedStyle]}>
          <Ionicons
            name="mail-outline"
            size={64}
            color={Colors.textSecondary}
          />
        </Animated.View>
        <Text style={styles.emptyTitle}>No New Announcements</Text>
        <Text style={styles.emptySubtitle}>
          You're all caught up! Check back later for new updates.
        </Text>
      </Animated.View>
    );
  };

  return (
    <View style={styles.container}>
      {hasAnnouncements ? (
        <FlatList
          data={announcementsData.anouncements}
          keyExtractor={(item) => item.id.toString()}
          renderItem={({ item }) => (
            <AnnouncementItem
              announcement={item}
              onPress={handleAnnouncementPress}
            />
          )}
          contentContainerStyle={styles.listContent}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={refetch}
              tintColor={Colors.primary}
              colors={[Colors.primary]}
            />
          }
        />
      ) : (
        <ScrollView
          contentContainerStyle={styles.container}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={refetch}
              tintColor={Colors.primary}
              colors={[Colors.primary]}
            />
          }
        >
          <EmptyState />
        </ScrollView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  listContent: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    overflow: "hidden",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: Colors.background,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 24,
    borderWidth: 2,
    borderColor: Colors.border,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: Colors.text,
    marginBottom: 8,
    textAlign: "center",
  },
  emptySubtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: "center",
    lineHeight: 22,
  },
});
