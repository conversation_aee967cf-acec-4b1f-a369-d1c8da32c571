// import { isAxiosError } from 'axios';
// import { VITE_S3_BASE_URL } from '@/env';

import { isValid } from "date-fns";

// export function errorHandler(ex: unknown) {
//   let errorMessage = 'Something went wrong';
//   if (isAxiosError(ex)) {
//     errorMessage = ex.response?.data?.message || ex.message || 'Error…';
//   } else if (ex instanceof Error) errorMessage = ex.message;

//   toast.error(errorMessage);
// }

/**
 * function to extract initials from name for ex: <PERSON>eq <PERSON>haik is SH and if no second name, just first letter initials
 * */

import { Colors } from "@/constants/Colors";
import { format } from "date-fns";
import Toast from "react-native-toast-message";

export function extractInitials(name: string): string {
  if (!name) return "";

  const words = name.trim().split(/\s+/);
  if (words.length === 1) {
    return words[0][0].toUpperCase();
  }

  return words[0][0].toUpperCase() + words[words.length - 1][0].toUpperCase();
}

export function formatMinutesToHourMinutes(numberOfMins: number): string {
  if (numberOfMins <= 0) return "0 mins";

  const hours = Math.floor(numberOfMins / 60);
  const minutes = numberOfMins % 60;

  if (hours === 0) return `${minutes} mins`;
  if (minutes === 0) return `${hours} hours`;

  return `${hours} hours, ${minutes} mins`;
}

export const formatFileSize = (size: number): string => {
  if (size < 1024) return `${size} B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`;
  if (size < 1024 * 1024 * 1024)
    return `${(size / (1024 * 1024)).toFixed(2)} MB`;
  return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
};

// export const s3url = (path: string | undefined | null) =>
//   path ? `${VITE_S3_BASE_URL}/${path}` : '';

export const enumToOptions = (enumObj: Record<string, string>) =>
  Object.values(enumObj).map((value) => ({
    value,
    label: value,
  }));

export const combineDateTime = (date: Date, time: string): Date => {
  const [hours, minutes] = time.split(":");
  const newDate = new Date(date);
  newDate.setHours(Number(hours));
  newDate.setMinutes(Number(minutes));
  newDate.setSeconds(0);

  return newDate;
};

export const isValidOrCurrentDate = (date: Date | string) =>
  isValid(new Date(date)) ? new Date(date) : new Date();

export const logger = (message: string, ...optionalParams: any[]) => {
  if (__DEV__) {
    console.log(message, ...optionalParams);
  }
};
type Announcement = {
  date?: string;
  [key: string]: any;
};

export function normalizeAnnouncements(data: Announcement[]): Announcement[] {
  return data.map((item) => {
    let formattedDate = "Invalid Date";

    try {
      const parsedDate = new Date(item.date ?? "");
      if (!isNaN(parsedDate.getTime())) {
        formattedDate = format(parsedDate, "dd-MM-yyyy");
      }
    } catch {
      formattedDate = "Invalid Date";
    }

    return {
      ...item,
      date: formattedDate,
    };
  });
}

export const successToast = (successText: string) => {
  Toast.show({
    type: "success",
    text1: `${successText} 🎉`,
    position: "top",
    text1Style: {
      fontSize: 16,
      color: Colors.primaryDark,
      fontWeight: "500",
    },
  });
};
export const errorToast = (error: any) => {
  Toast.show({
    type: "error",
    text1: "Error Submitting Claim 😞",
    text2: error?.message || "Something went wrong. Please try again.",
    position: "top",
    text1Style: { fontSize: 16, color: Colors.error, fontWeight: "500" },
  });
};

/**
 *
 * @param arr array of strings
 * @returns enum object
 */
export function createEnumFromArray(arr: string[]): Record<string, string> {
  const enumObj: Record<string, string> = {};

  arr.forEach((value) => {
    // Convert string to valid enum key (uppercase, replace spaces/special chars with underscores)
    const key = value
      .toUpperCase()
      .replace(/[^A-Z0-9]/g, "_")
      .replace(/_+/g, "_") // Replace multiple underscores with single
      .replace(/^_|_$/g, ""); // Remove leading/trailing underscores

    enumObj[key] = value;
  });

  return enumObj;
}
