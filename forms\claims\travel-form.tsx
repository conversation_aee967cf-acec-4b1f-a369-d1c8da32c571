import {
  <PERSON>Date<PERSON>icker,
  Form<PERSON>magePicker,
  FormInput,
  FormSubmitButton,
} from "@/components/forms";
import { Colors } from "@/constants/Colors";
import { ClaimType, useCreateClaimMutation } from "@/generated/graphql";
import { errorToast, successToast } from "@/lib/utils";
import { useSession } from "@/providers/auth-provider";
import { zodResolver } from "@hookform/resolvers/zod";
import React from "react";
import { useForm } from "react-hook-form";
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
} from "react-native";
import * as z from "zod";

const schema = z.object({
  amount: z.coerce.number().min(1, { message: "Amount is required" }),
  purpose: z.string().min(1, { message: "Reason is required" }),
  from: z.coerce.date({ invalid_type_error: "From Date is required" }),
  to: z.coerce.date({ invalid_type_error: "To Date is required" }),
  client: z.string().min(1, { message: "Client is required" }),
  toll: z.string().min(1, { message: "Toll is required" }),
  distance: z.coerce.number().min(1, { message: "Distance is required" }),
  receipts: z.array(z.string()).nullable(),
  claimType: z.nativeEnum(ClaimType),
  user: z.string().min(1),
});

type TravelForm = z.infer<typeof schema>;

export default function TravelForm() {
  const { session } = useSession();
  const { mutateAsync: createTravelClaim } = useCreateClaimMutation();

  const defaultTravelEntry: z.infer<typeof schema> = {
    amount: 0,
    purpose: "",
    from: undefined as unknown as Date,
    to: undefined as unknown as Date,
    client: "",
    toll: "",
    distance: 0,
    receipts: null,
    claimType: ClaimType.Travel,
    user: session?.fullname as string,
  };

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isValid },
  } = useForm<TravelForm>({
    defaultValues: defaultTravelEntry,
    resolver: zodResolver(schema),
    mode: "onChange",
  });

  // Handle form submission
  const onSubmit = async (data: TravelForm) => {
    try {
      // Call the mutation to create the travel claim
      await createTravelClaim({
        input: {
          ...data,
        },
      });
      successToast("Travel claim submitted successfully!");
      reset(defaultTravelEntry); // Reset form after successful submission
    } catch (error) {
      errorToast(error);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      keyboardVerticalOffset={100}
      style={styles.container}
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Amount Input */}
        <FormInput
          name={`amount`}
          control={control}
          label="Amount (RM)"
          placeholder="Enter Amount"
          keyboardType="numeric"
        />

        {/* Reason Input */}
        <FormInput
          name={`purpose`}
          control={control}
          label="Purpose"
          placeholder="Enter purpose.."
          multiline
          numberOfLines={3}
        />

        {/* Date Input */}
        <FormDatePicker
          name={`from`}
          control={control}
          label="From Date"
          placeholder="dd-mm-yy"
        />
        {/* Date Input */}
        <FormDatePicker
          name={`to`}
          control={control}
          label="To Date"
          placeholder="dd-mm-yy"
        />

        <FormInput
          name={`client`}
          control={control}
          label="Client name"
          placeholder="Enter client's name"
        />

        <FormInput
          name={`toll`}
          control={control}
          label="Toll Amount (RM)"
          placeholder="Enter toll"
          keyboardType="numeric"
        />

        <FormInput
          name={`distance`}
          control={control}
          label="Distance (km)"
          placeholder="Enter distance"
          keyboardType="numeric"
        />

        {/* Upload Invoice */}
        <FormImagePicker
          name={`receipts`}
          control={control}
          label="Upload Receipts"
        />

        {/* Submit Button */}
        <FormSubmitButton
          submitLabel="Submit"
          onSubmit={handleSubmit(onSubmit)}
          isValid={isValid}
          style={styles.submitButton}
        />
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingBottom: 50,
    paddingTop: 16,
  },
  submitButton: {
    marginTop: 20,
  },
});
