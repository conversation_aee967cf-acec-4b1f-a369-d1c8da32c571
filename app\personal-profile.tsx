import LoadingScreen from "@/components/LoadingView";
import UserProfileForm from "@/forms/personal-information/user-profile-form";
import { useUserProfileQuery } from "@/generated/graphql";
import { useSession } from "@/providers/auth-provider";
import React from "react";
import { StyleSheet, Text, View } from "react-native";

export default function PersonalProfile() {
  const { session } = useSession();

  const userId = session?.userId || "";

  const { data: userProfile, isLoading } = useUserProfileQuery({
    userId: userId,
  });

  const initialData = {
    ic: userProfile?.userProfile?.ic ?? "",
    ID: userProfile?.userProfile?.ID ?? "",
    passport: userProfile?.userProfile?.passport ?? "",
    passportExpiresAt: userProfile?.userProfile?.passportExpiresAt
      ? new Date(userProfile.userProfile.passportExpiresAt)
      : undefined,
    permitNumber: userProfile?.userProfile?.permitNumber ?? "",
    permitExpiresAt: userProfile?.userProfile?.permitExpiresAt
      ? new Date(userProfile.userProfile.permitExpiresAt)
      : undefined,
    gender:
      (userProfile?.userProfile?.gender as
        | "male"
        | "female"
        | "other"
        | undefined) ?? undefined,
    dob: userProfile?.userProfile?.dob
      ? new Date(userProfile.userProfile.dob)
      : undefined,
    placeOfBirth: userProfile?.userProfile?.placeOfBirth ?? "",
    currentAddress: userProfile?.userProfile?.currentAddress ?? "",
    joinedAt: userProfile?.userProfile?.joinedAt
      ? new Date(userProfile.userProfile.joinedAt)
      : undefined,
    maritalStatus:
      (userProfile?.userProfile?.maritalStatus as
        | "single"
        | "married"
        | "divorced"
        | "widowed"
        | undefined) ?? undefined,
    bankAccNumber: userProfile?.userProfile?.bankAccNumber ?? "",
    bankName: userProfile?.userProfile?.bankName ?? "",
    emergencyContact: userProfile?.userProfile?.emergencyContact ?? [],
  };

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>
        Fill the form to update your personal profile.
      </Text>
      {isLoading ? (
        <LoadingScreen />
      ) : (
        <UserProfileForm userId={userId} initialData={initialData} />
      )}
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingVertical: 8,
  },
  heading: {
    fontSize: 18,
    fontWeight: "500",
    marginBottom: 16,
    color: "#333",
    textDecorationLine: "underline",
    textDecorationStyle: "solid",
    textDecorationColor: "#333",
    paddingLeft: 16,
  },
});
