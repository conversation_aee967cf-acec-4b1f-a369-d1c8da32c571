import {
  FormDatePicker,
  FormImagePicker,
  FormInput,
  FormSubmitButton,
} from "@/components/forms";
import { Colors } from "@/constants/Colors";
import { ClaimType, useCreateClaimMutation } from "@/generated/graphql";
import { errorToast, successToast } from "@/lib/utils";
import { useSession } from "@/providers/auth-provider";
import { zodResolver } from "@hookform/resolvers/zod";
import React from "react";
import { useForm } from "react-hook-form";
import { KeyboardAvoidingView, Platform, StyleSheet } from "react-native";
import * as z from "zod";

const schema = z.object({
  from: z.coerce.date({ invalid_type_error: "From Date is required" }),
  to: z.coerce.date({ invalid_type_error: "To Date is required" }),
  purpose: z.string().min(1, { message: "Reason is required" }),
  workingHours: z.coerce
    .number()
    .min(1, { message: "Working hours is required" }),
  amount: z.coerce.number().min(1, { message: "Amount is required" }),
  receipts: z.array(z.string()).nullable(),
  user: z.string().min(1),
  claimType: z.nativeEnum(ClaimType),
});

type AllowanceForm = z.infer<typeof schema>;

export default function AllowanceForm() {
  const { session } = useSession();
  const defaultAllowanceEntry: z.infer<typeof schema> = {
    to: undefined as unknown as Date,
    from: undefined as unknown as Date,
    purpose: "",
    workingHours: 0,
    amount: 0,
    receipts: null,
    user: session?.fullname ?? "",
    claimType: ClaimType.Allowance,
  };

  const { mutateAsync: createAllowanceClaim } = useCreateClaimMutation();

  const {
    control,
    handleSubmit,
    reset,
    formState: { isValid },
  } = useForm<AllowanceForm>({
    defaultValues: defaultAllowanceEntry,
    resolver: zodResolver(schema),
    mode: "onChange",
  });

  // Handle form submission
  const onSubmit = async (data: AllowanceForm) => {
    try {
      await createAllowanceClaim({
        input: {
          ...data,
        },
      });
      successToast("Allowance claim submitted successfully!");
      reset(defaultAllowanceEntry); // Reset form after successful submission
    } catch (error) {
      errorToast(error);
    }
  };
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      keyboardVerticalOffset={100}
      style={styles.container}
    >
      {/* From Date Input */}
      <FormDatePicker
        name={`from`}
        control={control}
        label="From Date"
        placeholder="dd-mm-yy"
      />
      {/* To Date Input */}
      <FormDatePicker
        name={`to`}
        control={control}
        label="To Date"
        placeholder="dd-mm-yy"
      />

      {/* Reason Input */}
      <FormInput
        name={`purpose`}
        control={control}
        label="Purpose"
        placeholder="Type something..."
        multiline
        numberOfLines={3}
      />

      {/* Working Hours Input */}
      <FormInput
        name={`workingHours`}
        control={control}
        label="Working Hours"
        placeholder="Enter Hours"
        keyboardType="numeric"
      />

      {/* Amount Input */}
      <FormInput
        name={`amount`}
        control={control}
        label="Amount (RM)"
        placeholder="Enter Amount"
        keyboardType="numeric"
      />

      {/* Upload receipts */}
      <FormImagePicker
        name={`receipts`}
        control={control}
        label="Upload Receipts"
      />

      {/* Submit Button */}
      <FormSubmitButton
        submitLabel="Submit"
        onSubmit={handleSubmit(onSubmit)}
        isValid={isValid}
        style={styles.submitButton}
      />
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingBottom: 50,
    paddingTop: 16,
  },
  submitButton: {
    marginTop: 20,
  },
});
